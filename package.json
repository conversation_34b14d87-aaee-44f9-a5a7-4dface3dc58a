{"name": "wipdf", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"form-data": "^4.0.3", "lucide-react": "^0.525.0", "next": "15.3.4", "node-fetch": "^2.7.0", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.4", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}