'use client';

import { InvoiceData } from '@/types/invoice';

interface WindsurfInvoiceProps {
  data: InvoiceData;
}

export default function WindsurfInvoice({ data }: WindsurfInvoiceProps) {
  return (
    <div className="invoice-container">
      <style jsx>{`
        /* 精确匹配PDF的字体和样式 */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        .invoice-container {
          /* 精确匹配PDF的页面设置 */
          background-color: #f5f5f5;
          margin: 0;
          padding: 20px 0;
          display: flex;
          justify-content: center;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .page {
          /* 精确匹配PDF的A4纸张尺寸 */
          width: 210mm;
          height: 297mm;
          max-height: 297mm;

          /* 精确匹配PDF的边距和样式 */
          margin: 1cm 0;
          padding: 48px 48px 32px 48px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          background-color: #ffffff;

          /* 使用flexbox布局 */
          display: flex;
          flex-direction: column;

          box-sizing: border-box;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          color: #000000;
          font-size: 14px;
          line-height: 1.4;

          /* 防止分页 */
          page-break-inside: avoid;
          overflow: hidden;
        }

        .content-wrapper {
          flex-grow: 1;
        }

        /* 精确匹配PDF的头部样式 */
        .header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 32px;
        }

        .header h1 {
          font-size: 32px;
          font-weight: 700;
          margin: 0;
          line-height: 1.1;
          color: #000000;
        }

        .logo {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .logo img {
          width: auto;
          height: 32px;
        }

        /* 精确匹配PDF的Invoice信息区域 */
        .invoice-meta-section {
          margin-bottom: 32px;
        }

        .invoice-info table {
          border-collapse: collapse;
          width: auto;
        }

        .invoice-info td {
          padding: 2px 0;
          vertical-align: top;
          line-height: 1.5;
          font-size: 14px;
        }

        /* 标签样式 - 精确匹配PDF */
        .invoice-info td:first-child {
          color: #000000;
          padding-right: 24px;
          font-weight: 600;
          min-width: 140px;
        }

        /* 值样式 - 精确匹配PDF */
        .invoice-info td:last-child {
          font-weight: 600;
          color: #000000;
        }

        /* 精确匹配PDF的地址区域布局 */
        .address-section {
          display: flex;
          justify-content: space-between;
          margin-bottom: 40px;
          font-size: 14px;
        }

        .address {
          flex: 1;
          max-width: 280px;
        }

        .address p {
          margin: 0 0 4px 0;
          line-height: 1.4;
        }

        .address p:first-child {
          font-weight: 600;
          margin-bottom: 8px;
        }

        /* 精确匹配PDF的摘要区域 */
        .summary {
          margin-bottom: 32px;
        }

        .summary h2 {
          font-size: 18px;
          font-weight: 600;
          margin: 0;
          color: #000000;
        }

        /* 精确匹配PDF的表格样式 */
        .items-table {
          width: 100%;
          border-collapse: collapse;
          font-size: 14px;
          margin-bottom: 24px;
        }

        .items-table thead th {
          text-align: right;
          border-bottom: 1px solid #d1d5db;
          padding-bottom: 12px;
          padding-top: 8px;
          font-weight: 500;
          color: #000000;
          font-size: 14px;
        }

        .items-table thead th:first-child {
          text-align: left;
        }

        .items-table tbody td {
          padding: 16px 0;
          border-bottom: none;
          text-align: right;
          vertical-align: top;
          font-size: 14px;
        }

        .items-table tbody td:first-child {
          text-align: left;
        }

        /* 精确匹配PDF的列宽 */
        .items-table th:nth-child(1),
        .items-table td:nth-child(1) {
          width: 50%;
        }

        .items-table th:nth-child(2),
        .items-table td:nth-child(2) {
          width: 15%;
        }

        .items-table th:nth-child(3),
        .items-table td:nth-child(3) {
          width: 20%;
        }

        .items-table th:nth-child(4),
        .items-table td:nth-child(4) {
          width: 15%;
        }

        .item-description {
          color: #000000;
          font-weight: 500;
        }

        .item-date {
          font-size: 13px;
          margin-top: 4px;
          color: #000000;
        }
        
        /* 精确匹配PDF的总计区域 */
        .totals-section {
          display: flex;
          justify-content: flex-end;
          margin-top: 24px;
        }

        .totals-table {
          width: 280px;
          font-size: 14px;
          border-collapse: collapse;
        }

        .totals-table td {
          padding: 8px 0;
          border-top: 1px solid #e5e7eb;
        }

        .totals-table td:first-child {
          color: #000000;
          font-weight: 400;
        }

        .totals-table td:last-child {
          text-align: right;
          font-weight: 500;
          color: #000000;
        }

        .totals-table tr:last-child td {
          font-weight: 600;
          color: #000000;
          border-top: 2px solid #000000;
          padding-top: 12px;
        }

        /* 精确匹配PDF的页脚 */
        .footer {
          margin-top: auto;
          padding-top: 32px;
          border-top: 1px solid #e5e7eb;
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #000000;
        }

        /* 打印样式 */
        @media print {
          .invoice-container {
            background-color: #ffffff;
            margin: 0;
            padding: 0;
          }
          .page {
            width: 100%;
            height: 100vh;
            max-height: 100vh;
            margin: 0;
            box-shadow: none;
            border: none;
            padding: 48px 48px 32px 48px;
            page-break-inside: avoid;
            page-break-after: avoid;
            overflow: hidden;
          }
          .content-wrapper {
            height: calc(100vh - 80px);
            overflow: hidden;
          }
          .footer {
            margin-top: auto;
            page-break-inside: avoid;
          }
        }
      `}</style>

      <div className="page">
        <div className="content-wrapper">
          <div className="header">
            <h1>Receipt</h1>
            <div className="logo">
              <img src="/windsurf-logo.png" alt="Windsurf Logo" />
            </div>
          </div>

          <div className="invoice-meta-section">
            <div className="invoice-info">
              <table>
                <tbody>
                  <tr>
                    <td>Invoice number</td>
                    <td>{data.invoiceNumber}</td>
                  </tr>
                  <tr>
                    <td>Receipt number</td>
                    <td>{data.receiptNumber}</td>
                  </tr>
                  <tr>
                    <td>Date paid</td>
                    <td>{data.datePaid}</td>
                  </tr>
                  <tr>
                    <td>Payment method</td>
                    <td>{data.paymentMethod}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div className="address-section">
            <div className="address">
              <p>{data.companyInfo.name}</p>
              <p>{data.companyInfo.address1}</p>
              <p>{data.companyInfo.address2}</p>
              <p>{data.companyInfo.country}</p>
              {data.companyInfo.email && <p>{data.companyInfo.email}</p>}
              {data.companyInfo.taxInfo && <p>{data.companyInfo.taxInfo}</p>}
            </div>
            <div className="address">
              <p>Bill to</p>
              <p>{data.billTo.name}</p>
              <p>{data.billTo.postalCode}</p>
              <p>{data.billTo.address1}</p>
              <p>{data.billTo.address2}</p>
              <p>{data.billTo.city}</p>
              <p>{data.billTo.country}</p>
              <p>{data.billTo.email}</p>
              {data.billTo.website && <p>{data.billTo.website}</p>}
            </div>
          </div>

          <div className="summary">
            <h2>{data.amount} paid on {data.datePaid}</h2>
          </div>

          <table className="items-table">
            <thead>
              <tr>
                <th>Description</th>
                <th>Qty</th>
                <th>Unit price</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <div className="item-description">{data.description}</div>
                  <div className="item-date">{data.dateRange}</div>
                </td>
                <td>{data.quantity}</td>
                <td>{data.unitPrice}</td>
                <td>{data.amount}</td>
              </tr>
            </tbody>
          </table>

          <div className="totals-section">
            <table className="totals-table">
              <tbody>
                <tr>
                  <td>Subtotal</td>
                  <td>{data.subtotal}</td>
                </tr>
                <tr>
                  <td>Total</td>
                  <td>{data.total}</td>
                </tr>
                <tr>
                  <td>Amount paid</td>
                  <td>{data.amountPaid}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className="footer">
          <span>{data.receiptNumber} · {data.amount} paid on {data.datePaid}</span>
          <span>Page 1 of 1</span>
        </div>
      </div>
    </div>
  );
}
