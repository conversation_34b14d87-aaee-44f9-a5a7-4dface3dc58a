// 发票类型枚举
export enum InvoiceType {
  WINDSURF_6_90 = 'windsurf_6_90',
  WINDSURF_15 = 'windsurf_15'
}

// 公司信息接口
export interface CompanyInfo {
  name: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  country: string;
  email?: string;
  phone?: string;
  taxInfo?: string;
}

// 精确匹配PDF的账单信息接口
export interface BillToInfo {
  name: string;           // CHEN ZHANGQI
  postalCode: string;     // 100000
  address1: string;       // 北京市北京
  address2: string;       // 通州⽟桥
  city: string;           // 北京
  country: string;        // China
  email: string;          // <EMAIL>
  website?: string;       // tetech.edu
}

// 基础发票数据接口 - 精确匹配PDF格式
export interface InvoiceData {
  type: InvoiceType;
  invoiceNumber: string;    // 70C689900002 格式
  receiptNumber: string;    // 27640654 格式
  datePaid: string;         // April 17, 2025
  paymentMethod: string;    // American Express - 1116
  billTo: BillToInfo;
  amount: string;           // $6.90
  description: string;      // Windsurf Pro
  dateRange: string;        // Apr 17 – May 17, 2025
  companyInfo: CompanyInfo;

  // 新增字段以完全匹配PDF
  subtotal: string;         // $6.90
  total: string;            // $6.90
  amountPaid: string;       // $6.90
  quantity: number;         // 1
  unitPrice: string;        // $6.90
}

// 可编辑的Invoice数据接口
export interface EditableInvoiceData extends InvoiceData {
  isEditable?: boolean;
}

// PDF数据映射接口 - 用于精确复制PDF内容
export interface PDFDataMapping {
  // 发票基本信息
  invoiceNumber: string;
  receiptNumber: string;
  datePaid: string;
  paymentMethod: string;

  // 公司信息
  companyName: string;
  companyAddress1: string;
  companyAddress2: string;
  companyCountry: string;
  companyEmail: string;
  companyTaxInfo: string;

  // 客户信息
  customerName: string;
  customerPostalCode: string;
  customerAddress1: string;
  customerAddress2: string;
  customerCity: string;
  customerCountry: string;
  customerEmail: string;
  customerWebsite: string;

  // 产品和金额信息
  productDescription: string;
  serviceDate: string;
  quantity: number;
  unitPrice: string;
  amount: string;
  subtotal: string;
  total: string;
  amountPaid: string;
}
